_format_version: "3.0"
_transform: true
_info:
  select_tags:
    - ai-manager-created
    - "ai-manager-name: ram<PERSON><PERSON>"
    - ai-gw-workshop
    - ai-prompt-compressor
    - ai-proxy-advanced
    - "llm_v1_chat"

services:
- name: ai-gw
  url: http://host.docker.internal:8086
  routes:
  - name: chat
    paths:
    - /ai/chat    

plugins:
- name: ai-proxy-advanced
  service: ai-gw
  config:
    targets:
    - route_type: "llm/v1/chat"
      model:
        provider: "openai"
        name: "qwen2.5:0.5b-instruct"
        options:
          max_tokens: 1024
          temperature: 0.5
          upstream_url: http://host.docker.internal:8086/v1/chat/completions

- name: ai-prompt-compressor
  service: ai-gw
  config:
    compressor_type: target_token
    compressor_url: http://kong-compressor:8080
    log_text_data: true
    stop_on_error: true
    compression_ranges:
    - min_tokens: 1
      max_tokens: 50
      value: 25
    - min_tokens: 50
      max_tokens: 100
      value: 75
    - min_tokens: 100
      max_tokens: 1000
      value: 80

- name: cors
  config:
    origins:
    - "*"
    methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    headers:
    - "*"
    max_age: 3600
    credentials: true
