services:
  kafka:
    image: apache/kafka:latest
    container_name: kafka
    network_mode: host
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics.sh --bootstrap-server localhost:9092 --list" ]
      interval: 10s
      timeout: 10s
      retries: 5

  kong:
    image: kong/kong-gateway:3.11
    container_name: kong
    network_mode: host
    depends_on:
      - kafka
    env_file:
      - konnect.env
    environment:
        KONG_LOG_LEVEL: debug
