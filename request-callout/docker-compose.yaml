services:
  kong:
    image: kong/kong-gateway:3.10
    container_name: kong
    network_mode: host
    volumes:
        - ./kong-config/:/kong/declarative/
    environment:
        - KONG_DATABASE=off
        - KONG_DECLARATIVE_CONFIG=/kong/declarative/kong.yaml
        - KONG_PROXY_ACCESS_LOG=/dev/stdout
        - KONG_ADMIN_ACCESS_LOG=/dev/stdout
        - KONG_PROXY_ERROR_LOG=/dev/stderr
        - KONG_ADMIN_ERROR_LOG=/dev/stderr
        - KONG_ADMIN_LISTEN=0.0.0.0:8001
        - KONG_ADMIN_GUI_URL=http://localhost:8002
        - KONG_LICENSE_DATA
        # - KONG_LOG_LEVEL=debug
