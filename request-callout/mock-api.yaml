openapi: 3.0.0
info:
  title: Mock API
  version: 1.0.0
paths:
  /v1/user:
    get:
      responses:
        200:
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
              example:
                - id: 123
                  username: "<PERSON>"
                  address_id: 456
                - id: 124
                  username: "<PERSON>"
                  address_id: 457
  /v1/user/{id}:
    get:
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Single user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
              examples:
                user123:
                  value:
                    id: 123
                    username: "<PERSON>"
                    address_id: 456
                user124:
                  value:
                    id: 124
                    username: "<PERSON>"
                    address_id: 457
  /v1/address:
    get:
      responses:
        200:
          description: List of addresses
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Address'
              example:
                - id: 456
                  street: "123 Main St"
                  city: "New York"
                  country: "USA"
                - id: 457
                  street: "456 Oak Ave"
                  city: "San Francisco"
                  country: "USA"
  /v1/address/{id}:
    get:
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Single address
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Address'
              examples:
                address456:
                  value:
                    id: 456
                    street: "123 Main St"
                    city: "New York"
                    country: "USA"
                address457:
                  value:
                    id: 457
                    street: "456 Oak Ave"
                    city: "San Francisco"
                    country: "USA"
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          example: 123
        username:
          type: string
          example: "John Doe"
        address_id:
          type: integer
          example: 456
    Address:
      type: object
      properties:
        id:
          type: integer
          example: 456
        street:
          type: string
          example: "123 Main St"
        city:
          type: string
          example: "New York"
        country:
          type: string
          example: "USA"