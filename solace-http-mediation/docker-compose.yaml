name: kong-event-gw-dev

volumes:
  storage-group:

services:
  solace:
    container_name: solace
    image: solace/solace-pubsub-standard:latest
    volumes:
      - "storage-group:/var/lib/solace"
    shm_size: 1g
    ulimits:
      core: -1
      nofile:
        soft: 2448
        hard: 1048576
    deploy:
      restart_policy:
        condition: on-failure
        max_attempts: 1
    ports:
      #Web transport
      - '8008:8008'
      #Web transport over TLS
      - '1443:1443'
      #SEMP over TLS
      - '1943:1943'
      #SEMP / PubSub+ Manager
      - '8080:8080'
      #REST Default VPN
      - '9000:9000'
      #REST Default VPN over TLS
      - '9443:9443'
      #SMF
      - '55554:55555'
      #SMF Compressed
      - '55003:55003'
      #SMF over TLS
      - '55443:55443'
      #SSH connection to CLI
      - '2222:2222'
    environment:
      - username_admin_globalaccesslevel=admin
      - username_admin_password=admin
      - system_scaling_maxconnectioncount=100

  kong:
    image: kong/kong-gateway-dev:3.11.0.0-rc.8
    container_name: kong-docker
    ports:
      - "8000:8000"
      - "8443:8443"
    depends_on:
      - solace
    env_file:
      - konnect.env
    environment:
        KONG_LOG_LEVEL: debug

